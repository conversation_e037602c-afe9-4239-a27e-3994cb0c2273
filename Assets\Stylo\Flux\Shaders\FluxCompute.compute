#pragma kernel CSEncodeMain
#pragma kernel CSDecodeMain
#pragma kernel CSEncodeMainMobile
#pragma kernel CSDecodeMainMobile
#pragma kernel CSDebugMotionVectors
#pragma multi_compile_local _ PURE_DATAMOSH_MODE

#pragma kernel CSDebugCompression

// Define compute shader stage for Shared.cginc
#define SHADER_STAGE_COMPUTE

// Define PI constant for compute shader
#define PI 3.14159265359

// Input textures for compute shaders
Texture2D<float4> _InputTex;
Texture2D<float4> _EncodedTex;
Texture2D<float4> _PrevTex;
Texture2D<float4> _MotionVectorTex;
SamplerState sampler_LinearClamp;

// Compute shader specific constants
#define THREAD_GROUP_SIZE_X 16
#define THREAD_GROUP_SIZE_Y 16
#define THREAD_GROUP_SIZE_MOBILE_X 8
#define THREAD_GROUP_SIZE_MOBILE_Y 8

// Block size variants for DCT processing
#define DCT_BLOCK_2x2 0
#define DCT_BLOCK_4x4 1
#define DCT_BLOCK_8x8 2
#define DCT_BLOCK_16x16 3
#define DCT_BLOCK_32x32 4

// UAV textures for compute shader output
RWTexture2D<float4> _EncodeOutput : register(u0);
RWTexture2D<float4> _DecodeOutput : register(u1);
RWTexture2D<float4> _DebugOutput : register(u2);

// Compute buffers for intermediate data
RWStructuredBuffer<float4> _IntermediateBuffer : register(u3);
RWStructuredBuffer<float> _DCTCoefficients : register(u4);

// Compute shader specific uniforms
cbuffer FluxComputeUniforms
{
    int _BlockSize;
    int _ThreadGroupCountX;
    int _ThreadGroupCountY;
    float _ComputeIntensity;
    float _MobileOptimization;
    int _DebugMode;
    float _ComputeQuality;
    int _UseSharedMemory;
    float4 _Time; // Add _Time for compute shaders

    // Core Flux parameters
    float _ColorCrunch;
    float _EffectIntensity;
    int _Downscaling;
    float _ReprojectBaseNoise;
    float _ReprojectLengthInfluence;
    float _KeyframeResetRate;

    // Corruption parameters
    float _MotionVectorCorruption;
    float _ErrorAccumulation;
    float _DCTCorruption;
    float _ChromaCorruption;
    float _MultiScaleCorruption;

    // Motion processing parameters
    float _MotionAmplification;
    float _MotionThreshold;
    float _CameraObjectMotionBalance;
    float _MotionSmoothing;

    // Trail and flow parameters
    float _TrailIntensity;
    float _TrailSmoothness;
    float _TrailPersistence;
    float _FlowSpread;

    // JPEG compression parameters
    float _JPEGQuality;
    float _RingingArtifacts;
    float _MosquitoNoise;
    float _EdgeSensitivity;

    // Brightness control
    float _NoiseTransparency;
    float _MaxNoiseBrightness;
    float _BrightnessThreshold;
    float _BrightAreaMasking;

    // Compression parameters
    float _CompressionIntensity;
    float _LuminanceQuantization;
    float _ChrominanceQuantization;

    // Missing parameters found in compute shader usage
    float _CorruptionIntensity;
    float _QuantizationLevels;
    float _TemporalBlend;
    float _BrightnessMask;
    float _Gamma;
    float _Brightness;
    float _Contrast;

    // Screen parameters
    float4 _ScreenSize;
    float4 _Screen_TexelSize;
    float4 _Downscaled_TexelSize;

    // Sharpening parameter
    float _Sharpening;

    // JPG parity + Pure Datamosh controls (kept at end of cbuffer for layout stability)
    float _Use1080pMVScaling;   // 1 = use 1920x1080 MV normalization
    float _ReprojectSpeed;      // reroll speed
    float _ReprojectPercent;    // base noise percent
    float _PureDatamoshMode;    // 1 = pure mode
};


// Define _Input for compute shader compatibility with Shared.cginc
#define _Input _InputTex

// Include shared functionality AFTER cbuffer declaration so _Time is available
#include "Shared.cginc"

// Shared memory for DCT processing (16x16 max block)
groupshared float4 SharedPixelData[16][16];
groupshared float DCTSharedCoeffs[64]; // For 8x8 DCT blocks

// Enhanced hash function for compute shaders with better distribution
float4 ComputeHash(float2 coord, float time)
{
    float4 p = float4(coord.xy, time, coord.x + coord.y * time);
    p = frac(p * float4(443.897, 441.423, 437.195, 443.251));
    p += dot(p, p.yzwx + 19.19);
    return frac((p.xxyz + p.yzzw) * p.zywx);
}

// Simplified DCT processing without shared memory to avoid sync issues
float4 ProcessDCTBlock(uint2 blockCoord, uint2 localCoord, int blockSize)
{
    // Direct sampling approach - no shared memory needed
    uint2 globalCoord = blockCoord * blockSize + localCoord;
    float2 uv = globalCoord / _ScreenSize.xy;

    // Sample input directly
    float4 inputColor = _InputTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // Apply simple quantization based on block position
    float2 blockPos = fmod(globalCoord, blockSize);
    float quantization = _CompressionIntensity * (1.0 + length(blockPos) / blockSize);

    // Quantize color channels
    inputColor.rgb = round(inputColor.rgb / quantization) * quantization;

    return inputColor;
}

// DCT compression function for compute shaders (fallback implementation)
float4 ApplyDCTCompression(float2 uv, float4 originalColor, int blockSize)
{
    // Simplified DCT for compute - use the jpg() function from Shared.cginc
    return jpg(uv, 1); // Use mode 1 for basic DCT processing
}

// JPEG artifacts function for compute shaders
float4 ApplyJPEGArtifacts(float2 uv, float4 color)
{
    // Apply basic quantization and ringing artifacts
    float4 result = color;

    // Quantization
    if (_QuantizationLevels > 0)
    {
        result.rgb = floor(result.rgb * _QuantizationLevels) / _QuantizationLevels;
    }

    // Simple ringing artifacts simulation
    if (_RingingArtifacts > 0.001)
    {
        float2 texelSize = 1.0 / _ScreenSize.xy;
        float3 neighbors = 0;
        neighbors += _InputTex.SampleLevel(sampler_LinearClamp, uv + float2(texelSize.x, 0), 0).rgb;
        neighbors += _InputTex.SampleLevel(sampler_LinearClamp, uv - float2(texelSize.x, 0), 0).rgb;
        neighbors += _InputTex.SampleLevel(sampler_LinearClamp, uv + float2(0, texelSize.y), 0).rgb;
        neighbors += _InputTex.SampleLevel(sampler_LinearClamp, uv - float2(0, texelSize.y), 0).rgb;
        neighbors *= 0.25;

        float ringing = length(result.rgb - neighbors) * _RingingArtifacts;
        result.rgb += (neighbors - result.rgb) * ringing * 0.1;
    }

    return result;
}

// Simplified DCT for mobile compute kernels
float4 ApplySimplifiedDCT(float2 uv, float4 originalColor, int blockSize)
{
    // Use the same jpg() function but with reduced complexity
    return jpg(uv, 0); // Use mode 0 for simplified processing
}

// Optimized multi-scale corruption for compute shaders
float4 ApplyMultiScaleCorruption(uint2 coord, float4 originalColor, float time)
{
    float2 uv = coord / _ScreenSize.xy;
    float4 result = originalColor;

    // Multi-scale hash-based corruption
    [unroll]
    for (int scale = 0; scale < 4; scale++)
    {
        float2 scaledUV = uv * pow(2.0, scale);
        float4 noise = ComputeHash(scaledUV, time + scale * 0.1);

        float corruption = _CorruptionIntensity * pow(0.5, scale);
        result.rgb = lerp(result.rgb, noise.rgb, corruption * noise.a);
    }

    return result;
}

// Main encode kernel (desktop version)
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSEncodeMain(uint3 id : SV_DispatchThreadID, uint3 groupId : SV_GroupID, uint3 localId : SV_GroupThreadID)
{
    if (id.x >= _ScreenSize.x || id.y >= _ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / _ScreenSize.xy;
    float4 originalColor = _InputTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // Apply DCT processing - simplified to avoid varying flow control
    float4 processedColor = ApplyDCTCompression(uv, originalColor, _BlockSize);

    // Apply JPEG-style compression artifacts
    processedColor = ApplyJPEGArtifacts(uv, processedColor);

    // Apply color quantization
    processedColor.rgb = floor(processedColor.rgb * 256.0 / _QuantizationLevels) * _QuantizationLevels / 256.0;

    _EncodeOutput[id.xy] = processedColor;
}

// Main decode kernel (desktop version)
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSDecodeMain(uint3 id : SV_DispatchThreadID, uint3 groupId : SV_GroupID, uint3 localId : SV_GroupThreadID)
{
    if (id.x >= _ScreenSize.x || id.y >= _ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / _ScreenSize.xy;
    float4 encodedColor = _EncodedTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // PURE JPG datamosh parity in compute decode
    if (_PureDatamoshMode > 0.5)
    {
        float2 screenResolution = _Screen_TexelSize.zw;
        float2 mv = _MotionVectorTex.SampleLevel(sampler_LinearClamp, uv, 0).xy;
        float2 mvScale = (_Use1080pMVScaling > 0.5) ? float2(1920,1080) : screenResolution;
        float threshold = _ReprojectPercent + min(length(mv * mvScale) * _ReprojectLengthInfluence, 0.7);

        // hash per-block using 16x16 block id consistent with BLOCK_SIZE; fallback to 16
        int bs = max(_BlockSize, 2);
        int2 blockID = (int2)floor((uv * screenResolution) / bs);
        float h = hash1((uint)((123 + blockID.x) * (456 + blockID.y)) + (uint)floor(_Time.y * _ReprojectSpeed));
        if (h < threshold)
        {
            // safety: if prev frame tex is black/uninitialized, skip pull
            float4 prevColor = _PrevTex.SampleLevel(sampler_LinearClamp, uv - mv, 0);
            if (all(prevColor.rgb == 0.0))
            {
                _DecodeOutput[id.xy] = encodedColor;
                return;
            }
            // direct pull
            _DecodeOutput[id.xy] = prevColor;
            return;
        }
    }

    // Apply multi-scale corruption
    float4 corruptedColor = ApplyMultiScaleCorruption(id.xy, encodedColor, _Time.y);

    // JPG-Style Block-Centered Motion Vector Processing
    float2 screenResolution = _Screen_TexelSize.zw;
    int bs = max(_BlockSize, 2);
    float2 snappedUV = (floor(uv * screenResolution / bs) + 0.5) * bs / screenResolution;
    float2 motionVector = _MotionVectorTex.SampleLevel(sampler_LinearClamp, snappedUV, 0).xy;

    // JPG-Style Binary Reprojection Decision
    int2 blockID = (int2)floor((uv * screenResolution) / bs);
    float2 mvScale = (_Use1080pMVScaling > 0.5) ? float2(1920,1080) : screenResolution;
    float threshold = _ReprojectPercent + min(length(motionVector * mvScale) * _ReprojectLengthInfluence, 0.7);
    float h = hash1((uint)((123 + blockID.x) * (456 + blockID.y)) + (uint)floor(_Time.y * _ReprojectSpeed));

    if (h < threshold)
    {
        // Direct frame replacement like JPG
        float4 prevColor = _PrevTex.SampleLevel(sampler_LinearClamp, uv - motionVector, 0);
        if (any(prevColor.rgb > 0.0))
        {
            _DecodeOutput[id.xy] = prevColor;
            return;
        }
    }

    // No reprojection - use current corrupted color
    float4 result = corruptedColor;

    // Apply final color grading
    result.rgb = pow(result.rgb, 1.0 / _Gamma);
    result.rgb = result.rgb * _Brightness + _Contrast * (result.rgb - 0.5);

    _DecodeOutput[id.xy] = result;
}

// Mobile optimized encode kernel (reduced thread group size and simplified processing)
[numthreads(THREAD_GROUP_SIZE_MOBILE_X, THREAD_GROUP_SIZE_MOBILE_Y, 1)]
void CSEncodeMainMobile(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= _ScreenSize.x || id.y >= _ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / _ScreenSize.xy;
    float4 originalColor = _InputTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // Simplified DCT processing for mobile
    float4 processedColor = ApplySimplifiedDCT(uv, originalColor, min(_BlockSize, 4));

    // Reduced quantization levels for mobile
    int mobileQuantLevels = max(_QuantizationLevels / 2, 4);
    processedColor.rgb = floor(processedColor.rgb * 256.0 / mobileQuantLevels) * mobileQuantLevels / 256.0;

    _EncodeOutput[id.xy] = processedColor;
}


// Mobile optimized decode kernel
[numthreads(THREAD_GROUP_SIZE_MOBILE_X, THREAD_GROUP_SIZE_MOBILE_Y, 1)]
void CSDecodeMainMobile(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= _ScreenSize.x || id.y >= _ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / _ScreenSize.xy;
    float4 encodedColor = _EncodedTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // PURE JPG datamosh parity in compute decode (mobile)
    if (_PureDatamoshMode > 0.5)
    {
        float2 screenResolution = _Screen_TexelSize.zw;
        float2 mv = _MotionVectorTex.SampleLevel(sampler_LinearClamp, uv, 0).xy * 0.5; // mobile reduced influence still applies
        float2 mvScale = (_Use1080pMVScaling > 0.5) ? float2(1920,1080) : screenResolution;
        float threshold = _ReprojectPercent + min(length(mv * mvScale) * _ReprojectLengthInfluence, 0.7);
        int bs = max(_BlockSize, 2);
        int2 blockID = (int2)floor((uv * screenResolution) / bs);
        float h = hash1((uint)((123 + blockID.x) * (456 + blockID.y)) + (uint)floor(_Time.y * _ReprojectSpeed));
        if (h < threshold)
        {
            // safety: if prev frame tex is black/uninitialized, skip pull
            float4 prevColorCheck = _PrevTex.SampleLevel(sampler_LinearClamp, uv - mv, 0);
            if (all(prevColorCheck.rgb == 0.0))
            {
                _DecodeOutput[id.xy] = encodedColor;
                return;
            }
            _DecodeOutput[id.xy] = prevColorCheck;
            return;
        }
    }

    // Simplified multi-scale corruption for mobile (2 scales instead of 4)
    float4 corruptedColor = encodedColor;
    [unroll]
    for (int scale = 0; scale < 2; scale++)
    {
        float2 scaledUV = uv * pow(2.0, scale);
        float4 noise = ComputeHash(scaledUV, _Time.y + scale * 0.1);

        float corruption = _CorruptionIntensity * 0.5 * pow(0.5, scale);
        corruptedColor.rgb = lerp(corruptedColor.rgb, noise.rgb, corruption * noise.a);
    }

    // JPG-Style Block-Centered Motion Vector Processing (Mobile)
    float2 screenResolution = _Screen_TexelSize.zw;
    int bs = max(_BlockSize, 2);
    float2 snappedUV = (floor(uv * screenResolution / bs) + 0.5) * bs / screenResolution;
    float2 motionVector = _MotionVectorTex.SampleLevel(sampler_LinearClamp, snappedUV, 0).xy * 0.5; // Mobile reduced influence

    // JPG-Style Binary Reprojection Decision (Mobile)
    int2 blockID = (int2)floor((uv * screenResolution) / bs);
    float2 mvScale = (_Use1080pMVScaling > 0.5) ? float2(1920,1080) : screenResolution;
    float threshold = _ReprojectPercent + min(length(motionVector * mvScale) * _ReprojectLengthInfluence, 0.7);
    float h = hash1((uint)((123 + blockID.x) * (456 + blockID.y)) + (uint)floor(_Time.y * _ReprojectSpeed));

    if (h < threshold)
    {
        // Direct frame replacement like JPG (Mobile)
        float4 prevColor = _PrevTex.SampleLevel(sampler_LinearClamp, uv - motionVector, 0);
        if (any(prevColor.rgb > 0.0))
        {
            _DecodeOutput[id.xy] = prevColor;
            return;
        }
    }

    // No reprojection - use current corrupted color (Mobile)
    float4 result = corruptedColor;

    _DecodeOutput[id.xy] = result;
}

// Debug kernel for motion vector visualization
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSDebugMotionVectors(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= _ScreenSize.x || id.y >= _ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / _ScreenSize.xy;

    // Use JPG-style block-centered sampling for debug visualization
    float2 screenResolution = _Screen_TexelSize.zw;
    int bs = max(_BlockSize, 2);
    float2 snappedUV = (floor(uv * screenResolution / bs) + 0.5) * bs / screenResolution;
    float2 motionVector = _MotionVectorTex.SampleLevel(sampler_LinearClamp, snappedUV, 0).xy;

    // Visualize motion vectors as colored arrows
    float magnitude = length(motionVector);
    float angle = atan2(motionVector.y, motionVector.x);

    float3 color = float3(
        (sin(angle) + 1.0) * 0.5,
        (cos(angle) + 1.0) * 0.5,
        magnitude * 10.0
    );

    _DebugOutput[id.xy] = float4(color, 1.0);
}

// Debug kernel for compression artifact visualization
[numthreads(THREAD_GROUP_SIZE_X, THREAD_GROUP_SIZE_Y, 1)]
void CSDebugCompression(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= _ScreenSize.x || id.y >= _ScreenSize.y)
        return;

    float2 uv = (id.xy + 0.5) / _ScreenSize.xy;
    float4 originalColor = _InputTex.SampleLevel(sampler_LinearClamp, uv, 0);
    float4 compressedColor = _EncodedTex.SampleLevel(sampler_LinearClamp, uv, 0);

    // Visualize compression artifacts as difference map
    float4 difference = abs(originalColor - compressedColor);
    float intensity = dot(difference.rgb, float3(0.333, 0.333, 0.333));

    // Heat map visualization
    float3 heatColor = lerp(
        float3(0, 0, 1),  // Blue for low difference
        float3(1, 0, 0),  // Red for high difference
        saturate(intensity * 5.0)
    );

    _DebugOutput[id.xy] = float4(heatColor, 1.0);
}
