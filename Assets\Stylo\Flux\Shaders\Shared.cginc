#ifdef SHADER_STAGE_COMPUTE
    #define SAMPLE(texture, sampler, uv) texture.SampleLevel(sampler, uv, 0)
#elif HLSL
    #define SAMPLE(texture, sampler, uv) SAMPLE_TEXTURE2D_X(texture, sampler, uv)
#else
    #define SA<PERSON><PERSON>(texture, sampler, uv) UNITY_SAMPLE_SCREENSPACE_TEXTURE(texture, uv)
#endif

// Handle _Time for compute shaders vs fragment shaders
#ifdef SHADER_STAGE_COMPUTE
    // For compute shaders, _Time is passed via uniform buffer
    // (defined in FluxCompute.compute)
#else
    // For fragment shaders, use Unity's built-in _Time
    // (automatically available)
#endif

// Helper function to get time value consistently
float GetTimeValue()
{
    #ifdef SHADER_STAGE_COMPUTE
        return _Time.y; // Use compute shader _Time
    #else
        return _Time.y; // Use Unity built-in _Time
    #endif
}

#ifdef BLOCK_SIZE_2
    #define BLOCK_SIZE 2
#endif
#ifdef BLOCK_SIZE_4
    #define BLOCK_SIZE 4
#endif
#ifdef BLOCK_SIZE_8
    #define BLOCK_SIZE 8
#endif
#ifdef BLOCK_SIZE_16
    #define BLOCK_SIZE 16
#endif
#ifdef BLOCK_SIZE_32
    #define BLOCK_SIZE 32
#endif
#ifndef BLOCK_SIZE
    #define BLOCK_SIZE 4
#endif

// Hash function used throughout the shader
float hash1(uint n)
{
    n++;
    n = (n << 13U) ^ n;
    n = n * (n * n * 15731U + 789221U) + 1376312589U;
    return float(n & uint(0x7fffffffU))/float(0x7fffffff);
}

// Luminance calculation for brightness-based masking
float CalculateLuminance(float3 color)
{
    return dot(color, float3(0.2126, 0.7152, 0.0722));
}

// Brightness-based noise masking function
float CalculateBrightnessMask(float3 color, float brightnessThreshold, float maskingStrength)
{
    float luminance = CalculateLuminance(color);
    float brightnessFactor = saturate((luminance - brightnessThreshold) / (1.0 - brightnessThreshold));
    return lerp(1.0, 1.0 - maskingStrength, brightnessFactor);
}

// JPG-Style Block-Centered Motion Vector Sampling
// Simplified approach that matches JPG Bitcrunching Effect's fluid motion
float2 GetBlockCenteredMotionVector(float2 uv, float blockSize)
{
    // Calculate block-centered UV coordinates (JPG approach)
    float2 texelSize = _Downscaled_TexelSize.xy;
    float2 snappedUV = (floor(uv / (texelSize * blockSize)) + 0.5) * (texelSize * blockSize);

    // Sample motion vector at block center for coherent block movement
    // Use appropriate texture name based on shader stage
    #ifdef SHADER_STAGE_COMPUTE
        return _MotionVectorTex.SampleLevel(sampler_LinearClamp, snappedUV, 0).xy;
    #else
        return SAMPLE(_MotionVectorTexture, sampler_LinearClamp, snappedUV).xy;
    #endif
}

// Enhanced Motion Vector Processing
// Implements coordinate transformation and pixelated noise generation
float2 ProcessMotionVectorCoordinates(float2 baseUV, float2 motionVector, float intensity)
{
    // Guide's coordinate transformation approach
    // Add motion vector offset to base UV coordinates
    float2 processedMotion = motionVector * intensity;
    return baseUV + processedMotion;
}

float4 GeneratePixelatedNoise(float2 uv, float pixelationScale, float noiseScale)
{
    // Guide's pixelated noise system implementation
    // Create stepped/pixelated coordinates
    float2 pixelatedUV = floor(uv * pixelationScale) / pixelationScale;

    // Generate noise using pixelated coordinates
    float noise = hash1(uint(pixelatedUV.x * 1000.0 + pixelatedUV.y * 2000.0 + GetTimeValue() * noiseScale));
    return float4(noise, noise, noise, 1.0);
}

float2 EnhancedMotionVectorSampling(float2 uv, float2 motionVector, float blendFactor)
{
    // Guide's motion vector blending approach
    // Blend between base UV and motion-offset UV
    float2 motionOffsetUV = ProcessMotionVectorCoordinates(uv, motionVector, 1.0);
    return lerp(uv, motionOffsetUV, blendFactor);
}

//
float basis1D(float k, float i)
{
    float4 _G = float4(2, 1, 2, 2);
    float _Contrast = 0.0;
    return k == 0 ? sqrt(1. / float(BLOCK_SIZE)) : sqrt((_G.w + _Contrast) / float(BLOCK_SIZE)) * cos(float((_G.x * float(i) + _G.y) * k) * 3.14159265358 / (_G.z * float(BLOCK_SIZE)));
}
float basis2D(float2 jk, float2 xy)
{
    return basis1D(jk.x, xy.x) * basis1D(jk.y, xy.y);
}
float4 jpg(float2 uv, int m)
{
    // JPEG quality calculation - use user-controlled quality with JPG Bitcrunch fallback
    float _Quality = (_JPEGQuality > 0.001) ? (_JPEGQuality / 25.0) : 4.0; // Default to JPG Bitcrunch value if not set
    float quality = length(float2(_Quality, _Quality)); // Exact same calculation as JPG Bitcrunch
    float4 outColor = float4(0, 0, 0, 1);

    float2 textureSize = _Downscaled_TexelSize.zw;
    textureSize = floor(textureSize / 2.0) * 2.0;

    float2 coords = int2(textureSize * uv);
    float2 inBlock = coords % BLOCK_SIZE - m * 0.5;
    float2 block = coords - inBlock;

    // EXACT DCT implementation like JPG Bitcrunch - no complex features
    [loop]
    for (int2 xy = 0; xy.x < BLOCK_SIZE; xy.x++)
    {
        [loop]
        for (xy.y = 0; xy.y < BLOCK_SIZE; xy.y++)
        {
            outColor += SAMPLE(_Input, sampler_LinearClamp, float2(block + xy) / textureSize)
                            * basis2D(lerp(inBlock, xy, m), lerp(inBlock, xy, 1.0 - m));
        }
    }

    // Much more aggressive frequency filtering like JPG Bitcrunch
    outColor *= lerp(step(length(float2(inBlock)), quality), 1.0, m);
    return outColor;
}
//


#ifndef SHADER_STAGE_COMPUTE
float4 Downscale_Frag(Varyings input) : SV_Target
{
    return SAMPLE(_Input, sampler_LinearClamp, input.uv);
}

float4 Encode_Frag(Varyings input) : SV_Target
{
    float4 col = jpg(input.uv, 0);

    // 5. Multi-Scale Corruption - Different corruption at multiple scales
    if (_MultiScaleCorruption > 0.001)
    {
        #ifdef MOBILE_OPTIMIZED
            // Mobile optimization: Only use 2 scales instead of 3
            float2 mediumBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 8.0);
            float mediumCorruption = hash1(uint(mediumBlockPos.x * 333 + mediumBlockPos.y * 444 + GetTimeValue() * 4.0));

            float2 smallBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 2.0);
            float smallCorruption = hash1(uint(smallBlockPos.x * 555 + smallBlockPos.y * 666 + GetTimeValue() * 5.0));

            if (mediumCorruption < _MultiScaleCorruption * 0.6)
            {
                float brightnessCorruption = (hash1(uint(mediumBlockPos.x * 999 + mediumBlockPos.y * 111)) - 0.5) * _MultiScaleCorruption * 0.2;
                col.rgb *= (1.0 + brightnessCorruption);
            }

            if (smallCorruption < _MultiScaleCorruption * 0.8)
            {
                float3 noise = float3(
                    hash1(uint(smallBlockPos.x * 123 + smallBlockPos.y * 456)),
                    hash1(uint(smallBlockPos.x * 456 + smallBlockPos.y * 789)),
                    hash1(uint(smallBlockPos.x * 789 + smallBlockPos.y * 123))
                ) - 0.5;
                col.rgb += noise * _MultiScaleCorruption * 0.05;
            }
        #else
            // Desktop: Full 3-scale corruption
            float2 largeBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 32.0);
            float largeCorruption = hash1(uint(largeBlockPos.x * 111 + largeBlockPos.y * 222 + GetTimeValue() * 3.0));

            float2 mediumBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 8.0);
            float mediumCorruption = hash1(uint(mediumBlockPos.x * 333 + mediumBlockPos.y * 444 + GetTimeValue() * 4.0));

            float2 smallBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 2.0);
            float smallCorruption = hash1(uint(smallBlockPos.x * 555 + smallBlockPos.y * 666 + GetTimeValue() * 5.0));

            if (largeCorruption < _MultiScaleCorruption * 0.3)
            {
                col.rgb += (hash1(uint(largeBlockPos.x * 777 + largeBlockPos.y * 888)) - 0.5) * _MultiScaleCorruption * 0.2;
            }

            if (mediumCorruption < _MultiScaleCorruption * 0.5)
            {
                float brightnessCorruption = (hash1(uint(mediumBlockPos.x * 999 + mediumBlockPos.y * 111)) - 0.5) * _MultiScaleCorruption * 0.3;
                col.rgb *= (1.0 + brightnessCorruption);
            }

            if (smallCorruption < _MultiScaleCorruption * 0.7)
            {
                float3 noise = float3(
                    hash1(uint(smallBlockPos.x * 123 + smallBlockPos.y * 456)),
                    hash1(uint(smallBlockPos.x * 456 + smallBlockPos.y * 789)),
                    hash1(uint(smallBlockPos.x * 789 + smallBlockPos.y * 123))
                ) - 0.5;
                col.rgb += noise * _MultiScaleCorruption * 0.1;
            }
        #endif
    }

    // DCT Corruption - only when enabled (> 0) with brightness control
    if (_DCTCorruption > 0.001)
    {
        float2 blockPos = floor(input.uv * _Downscaled_TexelSize.zw / BLOCK_SIZE);
        float corruption = hash1(uint(blockPos.x * 123 + blockPos.y * 456 + GetTimeValue() * 5.0));

        if (corruption < _DCTCorruption)
        {
            // Corrupt specific frequency components
            float2 freqPos = fmod(input.uv * _Downscaled_TexelSize.zw, BLOCK_SIZE);
            float freqWeight = length(freqPos - BLOCK_SIZE * 0.5) / (BLOCK_SIZE * 0.5);

            // Apply brightness-based masking to DCT corruption
            float brightnessMask = CalculateBrightnessMask(col.rgb, _BrightnessThreshold, _BrightAreaMasking);

            // Higher frequencies get more corruption (typical of compression artifacts)
            float corruptionAmount = freqWeight * _DCTCorruption * brightnessMask;
            float3 corruptionNoise = (hash1(uint(blockPos.x * 789 + blockPos.y * 123 + GetTimeValue() * 7.0)) - 0.5) * corruptionAmount * 0.5;

            // Clamp corruption to prevent peak brightness
            corruptionNoise = min(corruptionNoise, _MaxNoiseBrightness - CalculateLuminance(col.rgb));
            col.rgb += corruptionNoise;
        }
    }

    // EXACT JPG Bitcrunch color crunching - this is the key visual effect!
    if(_ColorCrunch == 0.0) return col;
    #ifndef COLOR_CRUNCH_SKYBOX
        float depth = SAMPLE(_CameraDepthTexture, sampler_LinearClamp, input.uv).x;
        if (depth == 0.0 || depth == 1.0) return col;
    #endif

    // EXACT same truncation calculation as JPG Bitcrunch
    float truncation = log10(lerp(1.0, 0.0001, _ColorCrunch));
    col.rgb = round(col.rgb / truncation) * truncation;

    // Enhanced Feature: Additional JPEG quantization features
    if (_LuminanceQuantization > 0.0 || _ChrominanceQuantization > 0.0)
    {
        // Convert to YUV for separate quantization
        float3 yuv;
        yuv.x = dot(col.rgb, float3(0.299, 0.587, 0.114)); // Luminance
        yuv.y = dot(col.rgb, float3(-0.14713, -0.28886, 0.436)); // U (Cb)
        yuv.z = dot(col.rgb, float3(0.615, -0.51499, -0.10001)); // V (Cr)

        // Apply additional quantization
        if (_LuminanceQuantization > 0.0)
        {
            float lumaQuantization = log10(lerp(1.0, 0.001, _LuminanceQuantization));
            yuv.x = round(yuv.x / lumaQuantization) * lumaQuantization;
        }

        if (_ChrominanceQuantization > 0.0)
        {
            float chromaQuantization = log10(lerp(1.0, 0.001, _ChrominanceQuantization));
            yuv.yz = round(yuv.yz / chromaQuantization) * chromaQuantization;
        }

        // Convert back to RGB
        col.r = yuv.x + 1.13983 * yuv.z;
        col.g = yuv.x - 0.39465 * yuv.y - 0.58060 * yuv.z;
        col.b = yuv.x + 2.03211 * yuv.y;
        col.rgb = saturate(col.rgb);
    }

    return col;
}

float4 Decode_Frag(Varyings input) : SV_Target
{
    float4 col = jpg(input.uv, 1);
    col.a = 1.0;
    return col;
}


float4 Upscale_Pull_Frag(Varyings input) : SV_Target
{
    float2 uv = input.uv;
    
    float3 center = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, 0)).rgb;
    float3 col;
    if (_Sharpening > 0.0)
    {
        float3 up = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, 1)).rgb;
        float3 left = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(-1, 0)).rgb;
        float3 right = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(1, 0)).rgb;
        float3 down = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, -1)).rgb;
        _Sharpening *= 2.0;
        col = (1.0 + 4.0 * _Sharpening) * center - _Sharpening * (up + left + right + down);
    }
    else
    {
        col = center;
    }

    // MOTION VECTOR VISUALIZATION - Enable this to see motion vectors
    // #define VIZ_MOTION_VECTORS
    #ifdef VIZ_MOTION_VECTORS
        float2 mv = SAMPLE(_MotionVectorTexture, sampler_LinearClamp, uv).xy;
        // Scale motion vectors for visibility and show as color
        return 0.5 + float4(mv * 50.0, 0.0, 1.0);
    #endif

    #ifdef REPROJECTION
        // JPG-Style Block-Centered Motion Vector Sampling
        // Use consistent block-centered approach for fluid motion like JPG
        float2 snappedUV = (floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE)) + 0.5) * (_Downscaled_TexelSize.xy * BLOCK_SIZE);
        int2 blockID = floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE));

        // Always use block-centered sampling for coherent motion (JPG approach)
        float2 motionSampleUV = snappedUV;

        // Core Datamosh: Basic corruption control (always active)
        float corruptionMask = 1.0;
        float glitchTransition = 1.0;

        // Enhanced Feature: Corruption Mask - Control where corruption occurs
        #ifdef _CORRUPTIONMASK
            #if defined(SHADER_API_D3D11) || defined(SHADER_API_D3D12) || defined(SHADER_API_VULKAN) || defined(SHADER_API_METAL)
                // URP/HDRP style
                corruptionMask = SAMPLE_TEXTURE2D(_CorruptionMask, sampler_CorruptionMask, uv).r;
            #else
                // Built-in style
                corruptionMask = tex2D(_CorruptionMask, uv).r;
            #endif
            if (corruptionMask < 0.1) return float4(col, 1.0); // No corruption in masked areas
        #endif

        // Enhanced Feature: Keyframe Reset (I-Frame simulation) - only when enabled
        if (_KeyframeResetRate > 0.001)
        {
            float keyframeReset = hash1(uint(blockID.x * 789 + blockID.y * 456 + floor(GetTimeValue() * 60.0)));
            if (keyframeReset < _KeyframeResetRate)
                return float4(col, 1.0); // Reset to clean frame
        }

        // JPG-Style Simple Motion Vector Processing
        // Use block-centered motion vector sampling for fluid motion like JPG
        float2 motionVector = GetBlockCenteredMotionVector(uv, BLOCK_SIZE);

        // Calculate motion magnitude using screen resolution (JPG approach)
        float2 screenResolution = _Screen_TexelSize.zw;
        float motionMagnitude = length(motionVector * screenResolution);

        // Simple motion amplification (optional enhancement)
        if (_MotionAmplification > 0.001)
        {
            motionVector *= (1.0 + _MotionAmplification * 0.5);
        }

        // Enhanced Motion Vector Corruption with Pixelated Noise (works in all modes)
        if (_MotionVectorCorruption > 0.001)
        {
            // Original corruption method
            float2 corruption = float2(
                hash1(uint(blockID.x * 123 + blockID.y * 456 + GetTimeValue() * 10.0)) - 0.5,
                hash1(uint(blockID.x * 456 + blockID.y * 789 + GetTimeValue() * 10.0)) - 0.5
            ) * _MotionVectorCorruption * 0.1;

            // Pixelated noise enhancement with motion-based scale
            float pixelationScale = _MotionVectorCorruption * 100.0 + 10.0; // Motion-based scale with base value
            float4 pixelatedNoise = GeneratePixelatedNoise(uv, pixelationScale, 5.0);

            // Combine traditional corruption with pixelated noise using brightness controls
            float baseNoiseIntensity = _NoiseTransparency; // Use user-controlled transparency
            #ifdef PURE_DATAMOSH_MODE
                baseNoiseIntensity *= 0.5; // Reduced intensity in Pure mode
            #endif

            // Apply brightness-based masking to noise intensity
            float3 currentColor = SAMPLE(_Input, sampler_LinearClamp, uv).rgb;
            float brightnessMask = CalculateBrightnessMask(currentColor, _BrightnessThreshold, _BrightAreaMasking);
            float effectiveNoiseIntensity = baseNoiseIntensity * brightnessMask;

            // Clamp pixelated noise to prevent peak brightness
            float4 clampedPixelatedNoise = float4(min(pixelatedNoise.xyz, _MaxNoiseBrightness), pixelatedNoise.w);

            float2 enhancedCorruption = corruption + (clampedPixelatedNoise.xy - 0.5) * _MotionVectorCorruption * effectiveNoiseIntensity;
            motionVector += enhancedCorruption;
        }

        #ifdef COMPRESSION_ARTIFACTS
            // Enhanced Compression Artifacts: Ringing and Mosquito Noise
            if (_RingingArtifacts > 0.001 || _MosquitoNoise > 0.001)
            {
                // Debug mode: Show compression artifacts in bright colors
                if (_DebugCompressionArtifacts > 0.5)
                {
                    // Bright red overlay to show where compression artifacts are active
                    col.rgb = lerp(col.rgb, float3(1.0, 0.0, 0.0), 0.3);
                }
                // Improved edge detection for artifact placement
                float3 up = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, 1)).rgb;
                float3 left = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(-1, 0)).rgb;
                float3 right = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(1, 0)).rgb;
                float3 down = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, -1)).rgb;

                // Enhanced edge detection with better sensitivity
                float edgeDetection = length(col - up) + length(col - left) + length(col - right) + length(col - down);
                edgeDetection *= _EdgeSensitivity;

                // Lower threshold for more visible artifacts and add base artifact level
                float artifactThreshold = 0.05; // Reduced from 0.1 for more sensitivity
                float baseArtifactLevel = 0.1; // Always apply some artifacts even without strong edges

                if (edgeDetection > artifactThreshold || baseArtifactLevel > 0.0)
                {
                    float effectiveEdgeStrength = max(edgeDetection, baseArtifactLevel);

                    // Enhanced Ringing artifacts: Overshoot around edges with brightness control
                    if (_RingingArtifacts > 0.001)
                    {
                        float ringingFrequency = 12.0; // Increased frequency for more visible ringing
                        float2 ringingPattern = sin(uv * ringingFrequency * 3.14159);

                        // Apply brightness-based masking to ringing artifacts
                        float brightnessMask = CalculateBrightnessMask(col.rgb, _BrightnessThreshold, _BrightAreaMasking);

                        // Multi-directional ringing for more authentic compression artifacts
                        float ringingIntensity = _RingingArtifacts * effectiveEdgeStrength * brightnessMask;
                        float3 ringingEffect = float3(
                            ringingPattern.x * ringingIntensity * 0.3,
                            ringingPattern.y * ringingIntensity * 0.2,
                            (ringingPattern.x + ringingPattern.y) * ringingIntensity * 0.1
                        );

                        // Clamp ringing effect to prevent peak brightness
                        ringingEffect = min(ringingEffect, _MaxNoiseBrightness - CalculateLuminance(col.rgb));
                        col.rgb += ringingEffect;
                    }

                    // Enhanced Mosquito noise: High-frequency artifacts with brightness control
                    if (_MosquitoNoise > 0.001)
                    {
                        float mosquitoFrequency = 20.0; // Higher frequency for more visible mosquito noise

                        // Multi-layer mosquito noise for more authentic effect
                        float mosquitoNoise1 = sin(uv.x * mosquitoFrequency + GetTimeValue() * 15.0) *
                                             sin(uv.y * mosquitoFrequency + GetTimeValue() * 18.0);
                        float mosquitoNoise2 = sin(uv.x * mosquitoFrequency * 1.3 + GetTimeValue() * 12.0) *
                                             sin(uv.y * mosquitoFrequency * 0.7 + GetTimeValue() * 22.0);

                        float combinedMosquitoNoise = (mosquitoNoise1 + mosquitoNoise2 * 0.5) / 1.5;

                        // Apply brightness-based masking to mosquito noise
                        float brightnessMask = CalculateBrightnessMask(col.rgb, _BrightnessThreshold, _BrightAreaMasking);

                        // Enhanced intensity calculation - now includes motion bonus but doesn't require it
                        float mosquitoIntensity = _MosquitoNoise * effectiveEdgeStrength * brightnessMask;
                        if (motionMagnitude > 0.001)
                        {
                            mosquitoIntensity *= (1.0 + motionMagnitude * 2.0); // Motion bonus
                        }

                        // Apply mosquito noise with brightness clamping
                        float3 mosquitoEffect = combinedMosquitoNoise * mosquitoIntensity * 0.15;
                        mosquitoEffect = min(mosquitoEffect, _MaxNoiseBrightness - CalculateLuminance(col.rgb));
                        col.rgb += mosquitoEffect;
                    }
                }
            }
        #endif

        // JPG-Style Previous Frame Sampling
        // Simple offset using motion vector like JPG
        float3 pull = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - motionVector).rgb;

        // Safety check for uninitialized previous frame data (like JPG Bitcrunch)
        if (all(pull == 0.0)) return float4(col, 1.0);

        // Debug: Uncomment to visualize when previous frame data is available
        // if (any(pull > 0.0)) return float4(1, 0, 1, 1); // Magenta = previous frame data available

        // Debug: Motion Vector Visualization
        // return 0.5 + float4(motionVector * 50.0, 0.0, 1.0); // Visualize motion vectors

        // Debug: Reprojection Threshold Visualization
        // float debugThreshold = _ReprojectPercent + min(length(motionVector * float2(1920, 1080)) * _ReprojectLengthInfluence, 0.7);
        // if (debugThreshold > 0.1) return float4(0, 1, 0, 1); // Green = high reprojection chance

        // Trail Persistence disabled in single-control mode

        // Legacy enhanced features removed for JPG-style simplicity

        // JPG-Style Simple Reprojection Calculation
        // Use JPG Bitcrunching Effect's proven approach for fluid motion
        float2 mvScale = (_Use1080pMVScaling > 0.5) ? float2(1920,1080) : screenResolution;
        float reprojectThreshold = _ReprojectPercent + min(length(motionVector * mvScale) * _ReprojectLengthInfluence, 0.7);

        // Apply corruption mask if enabled (simple multiplication)
        reprojectThreshold *= corruptionMask;

        // JPG-Style Binary Reprojection Decision
        // Use hash-based randomization for authentic datamosh trails like JPG
        if (hash1((123 + blockID.x) * (456 + blockID.y) + (GetTimeValue() * _ReprojectSpeed)) < reprojectThreshold)
        {
            // Direct frame replacement like JPG Bitcrunching Effect
            // This creates the characteristic snappy datamosh transitions
            return float4(pull, 1.0);
        }
    #endif
    
    return float4(col, 1.0);
}
#endif

#ifndef SHADER_STAGE_COMPUTE
float4 CopyToPrev_Frag(Varyings input) : SV_Target
{
    return SAMPLE(_Input, sampler_LinearClamp, input.uv);
}
#endif
