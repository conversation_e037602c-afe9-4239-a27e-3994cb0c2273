# FluxSimple - Simplified JPG Bitcrunch Effect

## Overview
FluxSimple is a dramatically simplified version of the Flux datamosh effect that replicates JPG Bitcrunching behavior exactly with minimal parameters.

## Features
- **Only 7 main parameters** instead of 30+
- **Automatic JPG Bitcrunch parity** - always uses Pure Datamosh Mode
- **Quick preset buttons** for instant setup
- **Clear parameter names** that make sense
- **Uses existing FluxRendererFeature** - no duplicate code

## Setup

### 1. Add to Global Volume
1. Select your Global Volume GameObject
2. Click "Add Override" 
3. Choose "Post-processing > Flux Simple"

### 2. Quick Start
Click one of the preset buttons:
- **Subtle**: Light compression, minimal datamosh
- **Classic**: Matches JPG Bitcrunch defaults ⭐ **Recommended**
- **Extreme**: Heavy compression, strong datamosh
- **Performance**: Optimized for mobile/low-end hardware

### 3. Adjust Parameters
- **Effect Intensity**: Master control (0-1)
- **Datamosh Intensity**: Random block reprojection (0 = no datamosh effect)
- **Motion Sensitivity**: How much movement affects trailing
- **Color Crunch**: JPEG compression strength
- **Downscaling**: Performance vs quality (higher = better performance)

## Parameters

### Basic Settings
- **Color Crunch** (0-1): JPEG-style color quantization
- **Downscaling** (1-10): Resolution division before processing
- **Block Size**: Compression block size (4x4/8x8/16x16)

### Datamosh Motion
- **Datamosh Intensity** (0-1): Random block reprojection chance
- **Motion Sensitivity** (0-5): Motion vector influence on trailing
- **Update Speed** (0-20): How often blocks randomly update per second

### Quality
- **Oversharpening** (0-1): Add oversharpening for deep-fried look
- **Don't Crunch Skybox**: Exclude skybox from compression

## Testing Requirements

**IMPORTANT**: The datamosh effect only works under specific conditions:

1. **Play Mode**: Enter Play Mode (motion vectors not generated in Edit Mode)
2. **Game View**: Test in Game View (Scene View excludes motion vectors)
3. **Moving Objects**: Objects must be moving in the scene
4. **FluxRendererFeature**: Must be added and enabled in URP Renderer asset

## Performance Tips

- **Mobile**: Use 4x4 blocks and higher downscaling (12+)
- **Desktop**: 16x16 blocks with moderate downscaling (8-10)
- **Low-end**: Use "Performance" preset
- **High-end**: Use "Classic" or "Extreme" presets

## Technical Details

### How It Works
1. FluxSimple component maps its simple parameters to full FluxEffect parameters
2. Automatically enables Pure Datamosh Mode for authentic JPG behavior
3. Your existing FluxRendererFeature processes everything normally
4. All complex enhancement features are automatically disabled

### Parameter Mapping
- **Datamosh Intensity** → ReprojectBaseNoise
- **Motion Sensitivity** → ReprojectLengthInfluence  
- **Update Speed** → ReprojectBaseRerollSpeed
- **Pure Datamosh Mode**: Always enabled
- **Force Motion Vectors**: Always enabled for Unity 6 compatibility

## Troubleshooting

### No Effect Visible
- Check that FluxRendererFeature is added to URP Renderer asset
- Ensure Datamosh Intensity > 0
- Test in Game View during Play Mode
- Make sure objects are moving in scene

### Poor Performance
- Use higher Downscaling value (12+)
- Switch to 4x4 Block Size
- Lower Datamosh Intensity
- Use "Performance" preset

### Motion Vectors Not Working
- FluxSimple automatically requests motion vectors in Unity 6
- Make sure you're testing in Play Mode
- Check that objects have moving transforms

## Comparison with JPG Bitcrunching Effect

FluxSimple exactly replicates JPG Bitcrunching Effect behavior:
- ✅ Same DCT frequency filtering
- ✅ Same block-based motion vector sampling  
- ✅ Same temporal reprojection algorithm
- ✅ Same quality parameter (hardcoded to 4.0)
- ✅ Unity 6 Render Graph compatibility
- ✅ Much simpler parameter interface

## Advanced Usage

For more complex effects, you can still use the full FluxEffect component alongside FluxSimple. FluxSimple will automatically disable itself when FluxEffect is active and configured.