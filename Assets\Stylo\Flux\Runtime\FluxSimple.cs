#if URP_INSTALLED
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System;

namespace Stylo.Flux.Universal
{
    [System.Serializable]
    [VolumeComponentMenu("Post-processing/Flux Simple")]
    public sealed class FluxSimple : VolumeComponent, IPostProcessComponent
    {
        [Header("🎯 JPG BITCRUNCH SIMPLE")]
        
        [Tooltip("Master control for overall effect intensity")]
        public ClampedFloatParameter EffectIntensity = new ClampedFloatParameter(0.35f, 0f, 1f);

        [Header("Basic Settings")]
        [Tooltip("JPEG-style color quantization strength")]
        public ClampedFloatParameter ColorCrunch = new ClampedFloatParameter(1f, 0f, 1f);

        [Tooltip("Resolution division (higher = better performance)")]
        public ClampedIntParameter Downscaling = new ClampedIntParameter(10, 1, 10);

        [Tooltip("Compression block size")]
        public FluxBlockSizeParameter BlockSize = new FluxBlockSizeParameter(_BlockSize._16x16);

        [Header("Datamosh Motion")]
        [Tooltip("Random block reprojection chance (0 = no datamosh)")]
        public ClampedFloatParameter DatamoshIntensity = new ClampedFloatParameter(0.1f, 0f, 1f);
        
        [Tooltip("Motion sensitivity (how much movement affects trailing)")]
        public ClampedFloatParameter MotionSensitivity = new ClampedFloatParameter(0.1f, 0f, 5f);
        
        [Tooltip("How often blocks randomly update per second")]
        public ClampedFloatParameter UpdateSpeed = new ClampedFloatParameter(3f, 0f, 20f);

        [Header("Quality")]
        [Tooltip("Add oversharpening for deep-fried look")]
        public ClampedFloatParameter Oversharpening = new ClampedFloatParameter(0.2f, 0f, 1f);

        [Tooltip("Don't compress skybox/background")]
        public BoolParameter DontCrunchSkybox = new BoolParameter(false);

        [Header("Advanced (Optional)")]
        [Tooltip("Apply effect only to stencil-marked objects")]
        public BoolParameter OnlyStenciled = new BoolParameter(false);

        [Tooltip("Visualize motion vectors for debugging")]
        public BoolParameter VisualizeMotionVectors = new BoolParameter(false);

        public enum _BlockSize
        {
            [InspectorName("4x4 (Fast)")]_4x4 = 1,
            [InspectorName("8x8 (Medium)")]_8x8 = 2,
            [InspectorName("16x16 (Quality)")]_16x16 = 3
        }

        [Serializable]
        public sealed class FluxBlockSizeParameter : VolumeParameter<_BlockSize>
        {
            public FluxBlockSizeParameter(_BlockSize value, bool overrideState = false) : base(value, overrideState) { }
        }

        [System.NonSerialized]
        public bool showAdvancedOptions = false;

        public bool IsActive() => EffectIntensity.value > 0f;
        public bool IsTileCompatible() => false;

        /// <summary>
        /// Quick setup presets for common use cases
        /// </summary>
        public void ApplyPreset(DatamoshPreset preset)
        {
            switch (preset)
            {
                case DatamoshPreset.Subtle:
                    EffectIntensity.value = 0.2f;
                    DatamoshIntensity.value = 0.05f;
                    MotionSensitivity.value = 0.05f;
                    ColorCrunch.value = 0.5f;
                    Downscaling.value = 8;
                    BlockSize.value = _BlockSize._8x8;
                    Oversharpening.value = 0.1f;
                    break;

                case DatamoshPreset.Classic:
                    EffectIntensity.value = 0.35f;
                    DatamoshIntensity.value = 0.1f;
                    MotionSensitivity.value = 0.1f;
                    ColorCrunch.value = 1f;
                    Downscaling.value = 10;
                    BlockSize.value = _BlockSize._16x16;
                    Oversharpening.value = 0.2f;
                    break;

                case DatamoshPreset.Extreme:
                    EffectIntensity.value = 0.7f;
                    DatamoshIntensity.value = 0.3f;
                    MotionSensitivity.value = 0.5f;
                    ColorCrunch.value = 1f;
                    Downscaling.value = 15;
                    BlockSize.value = _BlockSize._16x16;
                    Oversharpening.value = 0.5f;
                    break;

                case DatamoshPreset.Performance:
                    EffectIntensity.value = 0.25f;
                    DatamoshIntensity.value = 0.08f;
                    MotionSensitivity.value = 0.08f;
                    ColorCrunch.value = 0.8f;
                    Downscaling.value = 12;
                    BlockSize.value = _BlockSize._4x4;
                    Oversharpening.value = 0.1f;
                    break;
            }

            // Enable overrides for all changed parameters
            EffectIntensity.overrideState = true;
            DatamoshIntensity.overrideState = true;
            MotionSensitivity.overrideState = true;
            ColorCrunch.overrideState = true;
            Downscaling.overrideState = true;
            BlockSize.overrideState = true;
            Oversharpening.overrideState = true;
            DontCrunchSkybox.overrideState = true;
        }

        public enum DatamoshPreset
        {
            Subtle,
            Classic,
            Extreme,
            Performance
        }
    }
}
#endif